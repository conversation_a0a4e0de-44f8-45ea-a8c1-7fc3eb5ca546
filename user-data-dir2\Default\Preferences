{"accessibility": {"captions": {"headless_caption_enabled": false, "live_caption_language": "cmn-Hans-CN"}}, "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "", "shortcuts_version": 1}, "autocomplete": {"retention_policy_last_version": 139}, "autofill": {"last_version_deduped": 139}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"window_placement": {"bottom": 1478, "left": 47, "maximized": false, "right": 1967, "top": 398, "work_area_bottom": 1019, "work_area_left": 0, "work_area_right": 1920, "work_area_top": 0}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 17230, "data_sharing": {"eligible_for_version_out_of_date_instant_message": false, "eligible_for_version_out_of_date_persistent_message": false, "has_shown_any_version_out_of_date_message": false}, "default_search_provider": {"guid": ""}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "e3bcddd2-8799-4845-a34f-fe64ce7390ee", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "139.0.7258.5"}, "gaia_cookie": {"periodic_report_time_2": "13399013820576993"}, "gcm": {"product_category_for_subtypes": "org.chromium.windows"}, "google": {"services": {"signin_scoped_device_id": "76d1cff6-7989-42e8-9871-a233e9b1f155"}}, "intl": {"selected_languages": "zh-CN,zh"}, "invalidation": {"per_sender_topics_to_handler": {"*************": {}}}, "media": {"engagement": {"schema_version": 5}}, "migrated_user_scripts_toggle": true, "ntp": {"num_personal_suggestions": 3}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PAGE_ENTITIES": true, "PRICE_TRACKING": true, "SAVED_TAB_GROUP": true, "V8_COMPILE_HINTS": true}}, "password_manager": {"account_store_migrated_to_os_crypt_async": true, "autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true}, "privacy_sandbox": {"first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "background_password_check": {"check_fri_weight": 9, "check_interval": "*************", "check_mon_weight": 6, "check_sat_weight": 6, "check_sun_weight": 6, "check_thu_weight": 9, "check_tue_weight": 9, "check_wed_weight": 9, "next_check_time": "*****************"}, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"https://[*.]qq.com,*": {"last_modified": "13399024371751444", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "initialized_translations": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"https://mp.weixin.qq.com:443,*": {"expiration": "13406800378416227", "last_modified": "13399024378416231", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 7}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "ondevice_languages_downloaded": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"https://mp.weixin.qq.com:443,*": {"last_modified": "13399024371752893", "setting": {"lastEngagementTime": 1.339902437175288e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 15.0, "rawScore": 14.8608}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "suspicious_notification_ids": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "139.0.7258.5", "creation_time": "13399013820333052", "exit_type": "Normal", "family_member_role": "not_in_family", "isolated_web_app": {"install": {"pending_initialization_count": 0}}, "last_engagement_time": "13399024371752880", "last_time_obsolete_http_credentials_removed": 1754549390.407659, "last_time_password_store_metrics_reported": 1754540250.584667, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "您的 Chromium", "password_hash_data_list": [], "were_old_google_logins_removed": true}, "safebrowsing": {"event_timestamps": {}, "metrics_last_log_time": "13399013820", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "segmentation_platform": {"client_result_prefs": "ClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQnPipnNDK5hcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAIYBCADELr4qZzQyuYX", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "13398911999000000", "uma_in_sql_start_time": "13399013820580174"}, "sessions": {"event_log": [{"crashed": false, "time": "13399013820578946", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13399013866115095", "type": 2, "window_count": 1}, {"crashed": false, "time": "13399013893836609", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13399013941844564", "type": 2, "window_count": 1}, {"crashed": false, "time": "13399022930395115", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13399023081825191", "type": 2, "window_count": 1}, {"crashed": false, "time": "13399023120953911", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13399023272197287", "type": 2, "window_count": 1}, {"crashed": false, "time": "13399023352086485", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13399023361685145", "type": 2, "window_count": 1}, {"crashed": false, "time": "13399023464890475", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13399023483919997", "type": 2, "window_count": 1}, {"crashed": false, "time": "13399024363755357", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13399024378398381", "type": 2, "window_count": 1}], "session_data_status": 5}, "signin": {"allowed": false, "cookie_clear_on_exit_migration_notice_complete": true}, "site_search_settings": {"overridden_keywords": []}, "spellcheck": {"dictionaries": ["zh-CN", "en-US"], "dictionary": ""}, "sync": {"data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_valuable": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": false, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": false, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": false, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": false, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": false, "send_tab_to_self": false, "sessions": false, "shared_tab_group_account_data": false, "shared_tab_group_data": false, "sharing_message": false, "themes": false, "user_consent": false, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "encryption_bootstrap_token_per_account_migration_done": true, "feature_status_for_sync_to_signin": 5, "passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "toolbar": {"pinned_cast_migration_complete": true, "pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "139"}}