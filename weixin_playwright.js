const { chromium } = require('playwright');
const client = require('./http_client');
const path = require('path');
const fs = require('fs');

// 随机延迟函数
const randomDelay = async (min, max) => {
    const delay = Math.floor(Math.random() * (max - min + 1) + min);
    await new Promise(resolve => setTimeout(resolve, delay));
};

// 模拟人类点击
async function humanClick(page, selector) {
    try {
        await page.waitForSelector(selector, { state: 'visible', timeout: 5000 });
        const element = await page.locator(selector);
        const box = await element.boundingBox();
        
        // 添加随机偏移
        const x = box.x + box.width/2 + (Math.random() * 10 - 5);
        const y = box.y + box.height/2 + (Math.random() * 10 - 5);
        
        // 平滑移动鼠标并点击
        await page.mouse.move(x, y);
        await randomDelay(50, 150);
        await page.mouse.down();
        await randomDelay(50, 150);
        await page.mouse.up();
    } catch(error) {
        console.log(error)
    }
}

// 模拟人类输入
async function humanType(page, selector, text) {
    await page.locator(selector).focus();
    for (const char of text) {
        await page.keyboard.type(char);
        // 每个字符之间添加随机延迟 (30-100ms)
        await randomDelay(30, 100);
    }
}

var PageInstance;
var ContextInstance;

async function createPage() {
    if (PageInstance == null) {
        const userDataDir = path.join("E:/qianduan/diandian/", "user-data-dir2")

        const context = ContextInstance = await chromium.launchPersistentContext(userDataDir, {
            headless: false,  // 显示浏览器界面
            viewport: null, // 使用默认视口
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
            args: [
                '--window-size=1920,1080',
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-blink-features=AutomationControlled',
                '--disable-infobars'
            ]
        });

        // 创建新页面 (launchPersistentContext 返回的是 context，不是 browser)
        const page = await context.newPage();
        
        // 修改 WebDriver 标记
        await page.addInitScript(() => {
            Object.defineProperty(navigator, 'webdriver', {
                get: () => false,
            });
        });
        
        PageInstance = page;
    }
    return PageInstance;
}

async function login() {
    const page = await createPage();
    const selector = '#header > div.banner > div > div > div.login__type__container.login__type__container__scan';
    await page.waitForSelector(selector);
    const element = page.locator(selector);
    const box = await element.boundingBox();
    await page.screenshot({
      path: 'scan.png',
      clip: {
        x: box.x,
        y: box.y,
        width: box.width,
        height: box.height
      }
    });
    console.log('截图已保存为: scan.png');
    await client.upload('http://**************/upload.php', 'scan.png').then(urs => {
        console.log('上传结果', urs);
    })
    await client.get('http://**************/zhorz.php?key=zhorz&content=%E6%89%AB%E7%A0%81%E7%99%BB%E5%BD%95%3Ahttp%3A%2F%2F**************%2Fscan.php');

    let currentUrl = await page.url();
    console.log('Initial URL:', currentUrl);
    
    var succ = false;
    var times = 1;
    while (!succ) {
        let newUrl = await page.url();
        console.log('监听地址是否变化：', newUrl);
        if (currentUrl != newUrl) {
            succ = true;
        }
        await randomDelay(1000, 2000);
        times += 1;
        if (times > 300) {
            console.log('登录超时');
            await ContextInstance.close();
            break;
        }
    }
}

// 使用示例
(async () => {
    try {
        const page = await createPage();
        var needLogin = true;
        try {
            await page.goto('https://mp.weixin.qq.com/', { waitUntil: 'domcontentloaded' });
            const s = '#header > div.banner > div > div > div.login__type__container.login__type__container__scan';
            await page.waitForSelector(s, { state: 'visible', timeout: 3000 });
            await page.locator(s);
        } catch (error) {
            needLogin = false;
        }                    
        try {
            await page.goto('https://mp.weixin.qq.com/', { waitUntil: 'domcontentloaded' });
            const s = '#header > div.banner > div > div > div.login__type__container.login__type__container__scan';
            await page.waitForSelector(s, { state: 'visible', timeout: 3000 });
            await page.locator(s);
        } catch (error) {
            needLogin = false;
        }
        if (needLogin) {
            console.log('需要登录');
            await login();
        }
        console.log('开始抓取数据');
        await randomDelay(500, 1000);
        // 等待2秒，获取当前链接上的token
        let newUrl = await page.url();
        const match = newUrl.match(/[?&]token=([^&]+)/);
        let token = match ? match[1] : '';
            
        // 监听网络响应
        page.on('response', async response => {
            const url = response.url();
            /**
             *   /cgi-bin/gamewxagsettingwap/getwxagranklistformp
             *   
             */
            if (response.request().resourceType() === 'xhr' || response.request().resourceType() === 'fetch') {
                if (new RegExp('\/cgi-bin\/gamewxagsettingwap\/getwxagranklistformp').test(url)) {
                    console.log('a', url);
                    const raw = await response.text();
                    var responseData = JSON.parse(raw);
                    if (responseData.errcode === 0) {
                        await client.post('http://**************/save_wx.php?type=2', responseData['data']['rank_game_list']);
                    }
                }
                if (new RegExp('\/cgi-bin\/gamewxagbdatawap\/getwxagstatmp').test(url)) {
                    console.log('b', url);
                    var postData = response.request().postData();
                    postData = JSON.parse(postData);
                    if (postData.table_index_list[0].index_list[0].stat_type == '1000402') {
                        const raw = await response.text();
                        var responseData = JSON.parse(raw);
                        
                        let keyword = [];
                        responseData['data']['table_data_list'][0]['row_list'].forEach(item => {
                            if (item["key_field_list"].length > 0 && item["key_field_list"][1]['label'] > 0) {
                                keyword.push(item['key_field_list'][2]['value']);
                            }
                        })
                        if(keyword.length>0){
                            await client.get('http://**************/zhorz.php?key=zhorz&content=异常关键词：'+keyword.join(','));
                        }
                        await client.post('http://**************/save_wx.php?type=3', responseData['data']['table_data_list'][0]['row_list']);
                    }
                }
            }
        })
    
        await page.goto('https://mp.weixin.qq.com/wxamp/frame/pluginRedirect/pluginRedirect?action=plugin_redirect&plugin_uin=1039&custom=path=/minigame/game-creativity&lang=zh_CN&token='+token, { waitUntil: 'load', timeout: 60000*2 })

        // 等待页面稳定，让网络请求有时间触发
        console.log('页面加载完成，等待数据请求...');
        await page.waitForTimeout(5 * 1000); // 等待5秒让页面发起网络请求

        await client.get('http://**************/zhorz.php?key=zhorz&content=%E5%BE%AE%E4%BF%A1%E6%8E%92%E8%A1%8C%E6%A6%9C%3Ahttp%3A%2F%2F**************%2Fchart_wx.php%EF%BC%9B%E7%83%AD%E6%90%9C%E8%AF%8D%E6%8E%92%E8%A1%8C%E6%A6%9Chttp%3A%2F%2F**************%2Fchart_hot.php');
        
    } catch(err) {
        const logPath = path.join(__dirname, 'error.log');
        const stackTrace = `[${new Date().toString()}] ERROR:\n${err.stack}\n\n`;
        fs.appendFileSync(logPath, stackTrace);
    }

    await ContextInstance.close();
})();
