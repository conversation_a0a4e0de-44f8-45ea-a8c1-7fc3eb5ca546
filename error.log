[2025-05-09T07:17:51.396Z] 执行失败: Navigation timeout of 60000 ms exceeded
[Fri May 09 2025 18:03:07 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Navigation timeout of 120000 ms exceeded
    at new Deferred (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:60:34)
    at Deferred.create (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:21:16)
    at new LifecycleWatcher (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\LifecycleWatcher.js:69:60)
    at CdpFrame.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\Frame.js:149:29)
    at CdpFrame.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:109:27)
    at CdpPage.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:574:43)
    at E:\qianduan\diandian\weixin.js:185:20

[Sat May 10 2025 18:00:26 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Sun May 11 2025 18:02:45 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Navigation timeout of 120000 ms exceeded
    at new Deferred (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:60:34)
    at Deferred.create (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:21:16)
    at new LifecycleWatcher (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\LifecycleWatcher.js:69:60)
    at CdpFrame.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\Frame.js:149:29)
    at CdpFrame.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:109:27)
    at CdpPage.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:574:43)
    at E:\qianduan\diandian\weixin.js:185:20

[Mon May 12 2025 18:03:33 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Navigation timeout of 120000 ms exceeded
    at new Deferred (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:60:34)
    at Deferred.create (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:21:16)
    at new LifecycleWatcher (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\LifecycleWatcher.js:69:60)
    at CdpFrame.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\Frame.js:149:29)
    at CdpFrame.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:109:27)
    at CdpPage.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:574:43)
    at E:\qianduan\diandian\weixin.js:185:20

[Tue May 13 2025 14:36:17 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Navigation timeout of 120000 ms exceeded
    at new Deferred (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:60:34)
    at Deferred.create (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:21:16)
    at new LifecycleWatcher (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\LifecycleWatcher.js:69:60)
    at CdpFrame.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\Frame.js:149:29)
    at CdpFrame.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:109:27)
    at CdpPage.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:574:43)
    at E:\qianduan\diandian\weixin.js:185:20

[Tue May 13 2025 18:00:06 GMT+0800 (中国标准时间)] ERROR:
Error: Failed to launch the browser process! undefined


TROUBLESHOOTING: https://pptr.dev/troubleshooting

    at ChildProcess.onClose (E:\qianduan\diandian\node_modules\@puppeteer\browsers\lib\cjs\launch.js:314:24)
    at ChildProcess.emit (node:events:531:35)
    at ChildProcess._handle.onexit (node:internal/child_process:294:12)

[Thu May 15 2025 18:03:52 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Navigation timeout of 120000 ms exceeded
    at new Deferred (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:60:34)
    at Deferred.create (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:21:16)
    at new LifecycleWatcher (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\LifecycleWatcher.js:69:60)
    at CdpFrame.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\Frame.js:149:29)
    at CdpFrame.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:109:27)
    at CdpPage.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:574:43)
    at E:\qianduan\diandian\weixin.js:185:20

[Fri May 16 2025 18:02:34 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Navigation timeout of 120000 ms exceeded
    at new Deferred (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:60:34)
    at Deferred.create (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:21:16)
    at new LifecycleWatcher (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\LifecycleWatcher.js:69:60)
    at CdpFrame.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\Frame.js:149:29)
    at CdpFrame.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:109:27)
    at CdpPage.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:574:43)
    at E:\qianduan\diandian\weixin.js:185:20

[Sat May 17 2025 18:00:28 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Sun May 18 2025 18:00:25 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Mon May 19 2025 18:00:33 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Wed May 21 2025 18:03:19 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Navigation timeout of 120000 ms exceeded
    at new Deferred (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:60:34)
    at Deferred.create (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:21:16)
    at new LifecycleWatcher (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\LifecycleWatcher.js:69:60)
    at CdpFrame.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\Frame.js:149:29)
    at CdpFrame.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:109:27)
    at CdpPage.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:574:43)
    at E:\qianduan\diandian\weixin.js:185:20

[Fri May 23 2025 18:00:29 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Sun May 25 2025 18:00:32 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Mon May 26 2025 17:46:12 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Mon May 26 2025 18:02:54 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Navigation timeout of 120000 ms exceeded
    at new Deferred (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:60:34)
    at Deferred.create (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:21:16)
    at new LifecycleWatcher (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\LifecycleWatcher.js:69:60)
    at CdpFrame.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\Frame.js:149:29)
    at CdpFrame.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:109:27)
    at CdpPage.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:574:43)
    at E:\qianduan\diandian\weixin.js:185:20

[Tue May 27 2025 17:46:03 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Tue May 27 2025 18:02:49 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Navigation timeout of 120000 ms exceeded
    at new Deferred (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:60:34)
    at Deferred.create (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:21:16)
    at new LifecycleWatcher (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\LifecycleWatcher.js:69:60)
    at CdpFrame.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\Frame.js:149:29)
    at CdpFrame.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:109:27)
    at CdpPage.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:574:43)
    at E:\qianduan\diandian\weixin.js:185:20

[Wed May 28 2025 17:46:01 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Wed May 28 2025 18:00:30 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Thu May 29 2025 09:44:22 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Thu May 29 2025 17:45:57 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Fri May 30 2025 17:46:16 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Fri May 30 2025 18:04:24 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Navigation timeout of 120000 ms exceeded
    at new Deferred (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:60:34)
    at Deferred.create (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:21:16)
    at new LifecycleWatcher (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\LifecycleWatcher.js:69:60)
    at CdpFrame.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\Frame.js:149:29)
    at CdpFrame.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:109:27)
    at CdpPage.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:574:43)
    at E:\qianduan\diandian\weixin.js:185:20

[Sat May 31 2025 17:45:57 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Sat May 31 2025 18:00:24 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Sun Jun 01 2025 17:45:58 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Sun Jun 01 2025 18:02:56 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Navigation timeout of 120000 ms exceeded
    at new Deferred (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:60:34)
    at Deferred.create (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:21:16)
    at new LifecycleWatcher (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\LifecycleWatcher.js:69:60)
    at CdpFrame.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\Frame.js:149:29)
    at CdpFrame.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:109:27)
    at CdpPage.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:574:43)
    at E:\qianduan\diandian\weixin.js:185:20

[Mon Jun 02 2025 17:45:57 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Mon Jun 02 2025 18:00:20 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Tue Jun 03 2025 17:46:01 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Tue Jun 03 2025 18:03:31 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Navigation timeout of 120000 ms exceeded
    at new Deferred (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:60:34)
    at Deferred.create (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:21:16)
    at new LifecycleWatcher (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\LifecycleWatcher.js:69:60)
    at CdpFrame.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\Frame.js:149:29)
    at CdpFrame.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:109:27)
    at CdpPage.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:574:43)
    at E:\qianduan\diandian\weixin.js:185:20

[Wed Jun 04 2025 17:46:03 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Thu Jun 05 2025 17:46:06 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Fri Jun 06 2025 17:46:31 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Fri Jun 06 2025 18:04:24 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Navigation timeout of 120000 ms exceeded
    at new Deferred (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:60:34)
    at Deferred.create (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:21:16)
    at new LifecycleWatcher (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\LifecycleWatcher.js:69:60)
    at CdpFrame.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\Frame.js:149:29)
    at CdpFrame.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:109:27)
    at CdpPage.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:574:43)
    at E:\qianduan\diandian\weixin.js:185:20

[Sat Jun 07 2025 17:45:59 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Sat Jun 07 2025 18:03:09 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Navigation timeout of 120000 ms exceeded
    at new Deferred (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:60:34)
    at Deferred.create (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:21:16)
    at new LifecycleWatcher (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\LifecycleWatcher.js:69:60)
    at CdpFrame.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\Frame.js:149:29)
    at CdpFrame.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:109:27)
    at CdpPage.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:574:43)
    at E:\qianduan\diandian\weixin.js:185:20

[Sun Jun 08 2025 17:45:59 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Sun Jun 08 2025 18:00:23 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Mon Jun 09 2025 18:03:51 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Navigation timeout of 120000 ms exceeded
    at new Deferred (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:60:34)
    at Deferred.create (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:21:16)
    at new LifecycleWatcher (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\LifecycleWatcher.js:69:60)
    at CdpFrame.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\Frame.js:149:29)
    at CdpFrame.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:109:27)
    at CdpPage.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:574:43)
    at E:\qianduan\diandian\weixin.js:185:20

[Tue Jun 10 2025 17:46:16 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Tue Jun 10 2025 18:02:38 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Navigation timeout of 120000 ms exceeded
    at new Deferred (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:60:34)
    at Deferred.create (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:21:16)
    at new LifecycleWatcher (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\LifecycleWatcher.js:69:60)
    at CdpFrame.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\Frame.js:149:29)
    at CdpFrame.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:109:27)
    at CdpPage.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:574:43)
    at E:\qianduan\diandian\weixin.js:185:20

[Wed Jun 11 2025 17:45:58 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Wed Jun 11 2025 18:03:15 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Navigation timeout of 120000 ms exceeded
    at new Deferred (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:60:34)
    at Deferred.create (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:21:16)
    at new LifecycleWatcher (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\LifecycleWatcher.js:69:60)
    at CdpFrame.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\Frame.js:149:29)
    at CdpFrame.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:109:27)
    at CdpPage.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:574:43)
    at E:\qianduan\diandian\weixin.js:185:20

[Thu Jun 12 2025 12:22:14 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Thu Jun 12 2025 12:23:15 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Thu Jun 12 2025 17:45:55 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Fri Jun 13 2025 17:45:59 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Fri Jun 13 2025 18:04:21 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Navigation timeout of 120000 ms exceeded
    at new Deferred (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:60:34)
    at Deferred.create (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:21:16)
    at new LifecycleWatcher (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\LifecycleWatcher.js:69:60)
    at CdpFrame.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\Frame.js:149:29)
    at CdpFrame.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:109:27)
    at CdpPage.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:574:43)
    at E:\qianduan\diandian\weixin.js:185:20

[Sat Jun 14 2025 17:45:59 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Sun Jun 15 2025 17:45:58 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Sun Jun 15 2025 18:03:02 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Navigation timeout of 120000 ms exceeded
    at new Deferred (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:60:34)
    at Deferred.create (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:21:16)
    at new LifecycleWatcher (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\LifecycleWatcher.js:69:60)
    at CdpFrame.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\Frame.js:149:29)
    at CdpFrame.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:109:27)
    at CdpPage.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:574:43)
    at E:\qianduan\diandian\weixin.js:185:20

[Mon Jun 16 2025 17:46:06 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Mon Jun 16 2025 18:01:43 GMT+0800 (中国标准时间)] ERROR:
Error: Navigating frame was detached
    at #onFrameDetached (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\LifecycleWatcher.js:103:47)
    at E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\third_party\mitt\mitt.js:62:7
    at Array.map (<anonymous>)
    at Object.emit (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\third_party\mitt\mitt.js:61:20)
    at CdpFrame.emit (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\EventEmitter.js:83:23)
    at #removeFramesRecursively (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\FrameManager.js:451:15)
    at #onClientDisconnect (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\FrameManager.js:94:42)

[Tue Jun 17 2025 17:46:01 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Tue Jun 17 2025 18:01:09 GMT+0800 (中国标准时间)] ERROR:
TargetCloseError: Protocol error (Network.setCacheDisabled): Target closed
    at CallbackRegistry.clear (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\CallbackRegistry.js:73:36)
    at CdpCDPSession._onClosed (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\CDPSession.js:101:25)
    at Connection.onMessage (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\Connection.js:131:25)
    at WebSocket.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\node\NodeWebSocketTransport.js:44:32)
    at callListener (E:\qianduan\diandian\node_modules\ws\lib\event-target.js:290:14)
    at WebSocket.onMessage (E:\qianduan\diandian\node_modules\ws\lib\event-target.js:209:9)
    at WebSocket.emit (node:events:519:28)
    at Receiver.receiverOnMessage (E:\qianduan\diandian\node_modules\ws\lib\websocket.js:1220:20)
    at Receiver.emit (node:events:519:28)
    at Immediate.<anonymous> (E:\qianduan\diandian\node_modules\ws\lib\receiver.js:601:16)

[Wed Jun 18 2025 17:45:57 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Wed Jun 18 2025 18:00:23 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Thu Jun 19 2025 17:45:58 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Thu Jun 19 2025 18:00:32 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Fri Jun 20 2025 17:46:17 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Fri Jun 20 2025 18:00:14 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Sat Jun 21 2025 17:46:00 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Sat Jun 21 2025 18:02:54 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Navigation timeout of 120000 ms exceeded
    at new Deferred (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:60:34)
    at Deferred.create (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:21:16)
    at new LifecycleWatcher (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\LifecycleWatcher.js:69:60)
    at CdpFrame.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\Frame.js:149:29)
    at CdpFrame.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:109:27)
    at CdpPage.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:574:43)
    at E:\qianduan\diandian\weixin.js:185:20

[Mon Jun 23 2025 17:45:56 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Mon Jun 23 2025 18:03:44 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Navigation timeout of 120000 ms exceeded
    at new Deferred (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:60:34)
    at Deferred.create (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:21:16)
    at new LifecycleWatcher (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\LifecycleWatcher.js:69:60)
    at CdpFrame.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\Frame.js:149:29)
    at CdpFrame.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:109:27)
    at CdpPage.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:574:43)
    at E:\qianduan\diandian\weixin.js:185:20

[Tue Jun 24 2025 18:00:06 GMT+0800 (中国标准时间)] ERROR:
Error: Failed to launch the browser process! undefined


TROUBLESHOOTING: https://pptr.dev/troubleshooting

    at ChildProcess.onClose (E:\qianduan\diandian\node_modules\@puppeteer\browsers\lib\cjs\launch.js:314:24)
    at ChildProcess.emit (node:events:531:35)
    at ChildProcess._handle.onexit (node:internal/child_process:294:12)

[Wed Jun 25 2025 17:46:01 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Wed Jun 25 2025 18:02:55 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Navigation timeout of 120000 ms exceeded
    at new Deferred (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:60:34)
    at Deferred.create (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:21:16)
    at new LifecycleWatcher (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\LifecycleWatcher.js:69:60)
    at CdpFrame.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\Frame.js:149:29)
    at CdpFrame.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:109:27)
    at CdpPage.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:574:43)
    at E:\qianduan\diandian\weixin.js:185:20

[Thu Jun 26 2025 17:46:00 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Thu Jun 26 2025 18:00:35 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Fri Jun 27 2025 17:46:01 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Fri Jun 27 2025 18:02:45 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Navigation timeout of 120000 ms exceeded
    at new Deferred (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:60:34)
    at Deferred.create (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:21:16)
    at new LifecycleWatcher (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\LifecycleWatcher.js:69:60)
    at CdpFrame.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\Frame.js:149:29)
    at CdpFrame.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:109:27)
    at CdpPage.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:574:43)
    at E:\qianduan\diandian\weixin.js:185:20

[Sat Jun 28 2025 17:46:02 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Sat Jun 28 2025 18:03:06 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Navigation timeout of 120000 ms exceeded
    at new Deferred (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:60:34)
    at Deferred.create (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:21:16)
    at new LifecycleWatcher (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\LifecycleWatcher.js:69:60)
    at CdpFrame.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\Frame.js:149:29)
    at CdpFrame.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:109:27)
    at CdpPage.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:574:43)
    at E:\qianduan\diandian\weixin.js:185:20

[Sun Jun 29 2025 17:46:01 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Sun Jun 29 2025 18:00:28 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Mon Jun 30 2025 17:45:57 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Mon Jun 30 2025 18:00:21 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Tue Jul 01 2025 17:46:47 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Wed Jul 02 2025 16:20:02 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Navigation timeout of 120000 ms exceeded
    at new Deferred (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:60:34)
    at Deferred.create (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:21:16)
    at new LifecycleWatcher (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\LifecycleWatcher.js:69:60)
    at CdpFrame.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\Frame.js:149:29)
    at CdpFrame.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:109:27)
    at CdpPage.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:574:43)
    at E:\qianduan\diandian\weixin.js:185:20

[Wed Jul 02 2025 16:29:41 GMT+0800 (中国标准时间)] ERROR:
Error: Attempted to use detached Frame 'FB56831DB8734A8FEC7A33121391B818'.
    at CdpFrame.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:107:23)
    at CdpPage.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:574:43)
    at E:\qianduan\diandian\tencent.js:184:20

[Wed Jul 02 2025 17:45:54 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Thu Jul 03 2025 17:46:10 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Fri Jul 04 2025 17:45:58 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Sat Jul 05 2025 17:46:01 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Sat Jul 05 2025 18:03:18 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Navigation timeout of 120000 ms exceeded
    at new Deferred (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:60:34)
    at Deferred.create (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:21:16)
    at new LifecycleWatcher (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\LifecycleWatcher.js:69:60)
    at CdpFrame.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\Frame.js:149:29)
    at CdpFrame.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:109:27)
    at CdpPage.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:574:43)
    at E:\qianduan\diandian\weixin.js:185:20

[Sun Jul 06 2025 17:45:58 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Sun Jul 06 2025 18:00:22 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Mon Jul 07 2025 11:46:12 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Navigation timeout of 120000 ms exceeded
    at new Deferred (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:60:34)
    at Deferred.create (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:21:16)
    at new LifecycleWatcher (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\LifecycleWatcher.js:69:60)
    at CdpFrame.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\Frame.js:149:29)
    at CdpFrame.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:109:27)
    at CdpPage.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:574:43)
    at E:\qianduan\diandian\weixin.js:185:20

[Mon Jul 07 2025 17:46:38 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Mon Jul 07 2025 18:05:55 GMT+0800 (中国标准时间)] ERROR:
Error: Navigating frame was detached
    at #onFrameDetached (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\LifecycleWatcher.js:103:47)
    at E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\third_party\mitt\mitt.js:62:7
    at Array.map (<anonymous>)
    at Object.emit (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\third_party\mitt\mitt.js:61:20)
    at CdpFrame.emit (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\EventEmitter.js:83:23)
    at #removeFramesRecursively (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\FrameManager.js:451:15)
    at #onClientDisconnect (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\FrameManager.js:94:42)

[Tue Jul 08 2025 09:36:47 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Navigation timeout of 120000 ms exceeded
    at new Deferred (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:60:34)
    at Deferred.create (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:21:16)
    at new LifecycleWatcher (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\LifecycleWatcher.js:69:60)
    at CdpFrame.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\Frame.js:149:29)
    at CdpFrame.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:109:27)
    at CdpPage.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:574:43)
    at E:\qianduan\diandian\weixin.js:185:20

[Tue Jul 08 2025 09:53:25 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Tue Jul 08 2025 09:55:57 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Tue Jul 08 2025 17:46:15 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Wed Jul 09 2025 17:46:05 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Wed Jul 09 2025 18:00:30 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Thu Jul 10 2025 17:45:59 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Fri Jul 11 2025 17:45:56 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Fri Jul 11 2025 18:00:15 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Sat Jul 12 2025 17:45:55 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Sat Jul 12 2025 18:00:16 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Sun Jul 13 2025 17:45:55 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Sun Jul 13 2025 18:00:16 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Mon Jul 14 2025 17:45:57 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Mon Jul 14 2025 18:00:19 GMT+0800 (中国标准时间)] ERROR:
Error: Attempted to use detached Frame '677244D3A0828718B3531153D0E924AF'.
    at CdpFrame.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:107:23)
    at CdpPage.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:574:43)
    at E:\qianduan\diandian\weixin.js:185:20

[Tue Jul 15 2025 14:48:51 GMT+0800 (中国标准时间)] ERROR:
Error: Attempted to use detached Frame 'F11E4A76989A543B57B8D36B507E8565'.
    at CdpFrame.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:107:23)
    at CdpPage.goto (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:574:43)
    at E:\qianduan\diandian\weixin.js:185:20

[Tue Jul 15 2025 17:45:19 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\tencent.js:54:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Tue Jul 15 2025 18:00:06 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Wed Jul 16 2025 17:45:33 GMT+0800 (中国标准时间)] ERROR:
TargetCloseError: Waiting for selector `#wxLoginCt` failed: Protocol error (Runtime.evaluate): Session closed. Most likely the page has been closed.
    at CdpCDPSession.send (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\CDPSession.js:64:35)
    at next (E:\qianduan\diandian\node_modules\puppeteer-extra-plugin-stealth\evasions\sourceurl\index.js:34:41)
    at CdpCDPSession.send (E:\qianduan\diandian\node_modules\puppeteer-extra-plugin-stealth\evasions\sourceurl\index.js:75:16)
    at #evaluate (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\ExecutionContext.js:342:18)
    at ExecutionContext.evaluateHandle (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\ExecutionContext.js:330:36)
    at E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\ExecutionContext.js:221:29

[Wed Jul 16 2025 18:00:09 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Thu Jul 17 2025 17:45:37 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Thu Jul 17 2025 18:00:09 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Fri Jul 18 2025 17:45:28 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Fri Jul 18 2025 18:00:06 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Sat Jul 19 2025 17:45:27 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Sat Jul 19 2025 18:00:07 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Sun Jul 20 2025 17:45:27 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Sun Jul 20 2025 18:00:07 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Mon Jul 21 2025 17:45:23 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\tencent.js:54:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Mon Jul 21 2025 18:00:10 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Tue Jul 22 2025 17:45:23 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\tencent.js:54:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Tue Jul 22 2025 18:00:06 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Wed Jul 23 2025 17:45:29 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Wed Jul 23 2025 18:00:10 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Thu Jul 24 2025 17:45:30 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\tencent.js:54:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Thu Jul 24 2025 18:00:08 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Fri Jul 25 2025 17:45:31 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Fri Jul 25 2025 18:00:06 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Sat Jul 26 2025 17:45:28 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Sat Jul 26 2025 18:00:08 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Sun Jul 27 2025 17:45:28 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Sun Jul 27 2025 18:00:08 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Mon Jul 28 2025 18:00:12 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Tue Jul 29 2025 17:45:44 GMT+0800 (中国标准时间)] ERROR:
Error: Attempted to use detached Frame '2215517D345973240E0969F6C21BA17D'.
    at CdpFrame.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:107:23)
    at CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:43)
    at login (E:\qianduan\diandian\tencent.js:51:16)
    at async E:\qianduan\diandian\tencent.js:143:13

[Tue Jul 29 2025 18:00:11 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Wed Jul 30 2025 17:45:20 GMT+0800 (中国标准时间)] ERROR:
TargetCloseError: Waiting for selector `#wxLoginCt` failed: Protocol error (Runtime.callFunctionOn): Target closed
    at CallbackRegistry.clear (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\CallbackRegistry.js:73:36)
    at CdpCDPSession._onClosed (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\CDPSession.js:101:25)
    at Connection.onMessage (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\Connection.js:131:25)
    at WebSocket.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\node\NodeWebSocketTransport.js:44:32)
    at callListener (E:\qianduan\diandian\node_modules\ws\lib\event-target.js:290:14)
    at WebSocket.onMessage (E:\qianduan\diandian\node_modules\ws\lib\event-target.js:209:9)
    at WebSocket.emit (node:events:519:28)
    at Receiver.receiverOnMessage (E:\qianduan\diandian\node_modules\ws\lib\websocket.js:1220:20)
    at Receiver.emit (node:events:519:28)
    at Immediate.<anonymous> (E:\qianduan\diandian\node_modules\ws\lib\receiver.js:601:16)

[Thu Jul 31 2025 17:45:23 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\tencent.js:54:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Fri Aug 01 2025 17:45:44 GMT+0800 (中国标准时间)] ERROR:
TimeoutError: Waiting for selector `#wxLoginCt` failed: Waiting failed: 10000ms exceeded
    at new WaitTask (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Realm.js:25:26)
    at CSSQueryHandler.waitFor (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Frame.js:532:21)
    at async CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:20)
    at async login (E:\qianduan\diandian\tencent.js:51:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Fri Aug 01 2025 18:01:13 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Sat Aug 02 2025 17:45:29 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\tencent.js:54:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Sat Aug 02 2025 18:00:10 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Sun Aug 03 2025 17:45:28 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\tencent.js:54:5)
    at async E:\qianduan\diandian\tencent.js:143:13

[Sun Aug 03 2025 18:00:11 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Tue Aug 05 2025 17:45:24 GMT+0800 (中国标准时间)] ERROR:
Error: Attempted to use detached Frame '65509AEE9046ACD87F5B7169D42A1A34'.
    at CdpFrame.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:107:23)
    at CdpPage.waitForSelector (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1374:43)
    at login (E:\qianduan\diandian\tencent.js:51:16)
    at async E:\qianduan\diandian\tencent.js:143:13

[Tue Aug 05 2025 18:00:04 GMT+0800 (中国标准时间)] ERROR:
TargetCloseError: Protocol error (Target.setDiscoverTargets): Target closed
    at CallbackRegistry.clear (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\common\CallbackRegistry.js:73:36)
    at #onClose (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\Connection.js:165:25)
    at WebSocket.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\node\NodeWebSocketTransport.js:49:30)
    at callListener (E:\qianduan\diandian\node_modules\ws\lib\event-target.js:290:14)
    at WebSocket.onClose (E:\qianduan\diandian\node_modules\ws\lib\event-target.js:220:9)
    at WebSocket.emit (node:events:519:28)
    at WebSocket.emitClose (E:\qianduan\diandian\node_modules\ws\lib\websocket.js:272:10)
    at Receiver.receiverOnFinish (E:\qianduan\diandian\node_modules\ws\lib\websocket.js:1209:20)
    at Receiver.emit (node:events:519:28)
    at finish (node:internal/streams/writable:953:10)

[Wed Aug 06 2025 18:00:06 GMT+0800 (中国标准时间)] ERROR:
Error: EPERM: operation not permitted, open 'C:\WINDOWS\system32\scan.png'
    at async open (node:internal/fs/promises:639:25)
    at async Object.writeFile (node:internal/fs/promises:1212:14)
    at async CdpPage._maybeWriteTypedArrayToFile (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:821:13)
    at async CdpPage.screenshot (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:1072:17)
    at async CdpPage.<anonymous> (E:\qianduan\diandian\node_modules\puppeteer-core\lib\cjs\puppeteer\util\decorators.js:172:24)
    at async login (E:\qianduan\diandian\weixin.js:92:5)
    at async E:\qianduan\diandian\weixin.js:143:13

[Thu Aug 07 2025 11:22:21 GMT+0800 (中国标准时间)] ERROR:
page.goto: Timeout 120000ms exceeded.
Call log:
[2m  - navigating to "https://mp.weixin.qq.com/wxamp/frame/pluginRedirect/pluginRedirect?action=plugin_redirect&plugin_uin=1039&custom=path=/minigame/game-creativity&lang=zh_CN&token=1459557247", waiting until "networkidle"[22m

    at E:\qianduan\diandian\weixin_playwright.js:177:20

[Thu Aug 07 2025 11:30:59 GMT+0800 (中国标准时间)] ERROR:
Error: userDataDir option is not supported in `browserType.launch`. Use `browserType.launchPersistentContext` instead
    at assert (E:\qianduan\diandian\node_modules\playwright-core\lib\utils\isomorphic\assert.js:26:11)
    at BrowserType.launch (E:\qianduan\diandian\node_modules\playwright-core\lib\client\browserType.js:52:30)
    at createPage (E:\qianduan\diandian\weixin_playwright.js:51:58)
    at E:\qianduan\diandian\weixin_playwright.js:128:28
    at Object.<anonymous> (E:\qianduan\diandian\weixin_playwright.js:189:3)
    at Module._compile (node:internal/modules/cjs/loader:1469:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)
    at Module.load (node:internal/modules/cjs/loader:1288:32)
    at Module._load (node:internal/modules/cjs/loader:1104:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:174:12)

[Thu Aug 07 2025 14:51:21 GMT+0800 (中国标准时间)] ERROR:
page.goto: Timeout 120000ms exceeded.
Call log:
[2m  - navigating to "https://mp.weixin.qq.com/wxamp/frame/pluginRedirect/pluginRedirect?action=plugin_redirect&plugin_uin=1039&custom=path=/minigame/game-creativity&lang=zh_CN&token=103147938", waiting until "networkidle"[22m

    at E:\qianduan\diandian\weixin_playwright.js:186:20

